# D1数据库迁移指南

## 🎯 概述

本指南将帮助您将Cloudflare Workers订阅代理服务从KV存储迁移到D1数据库。D1数据库提供了更好的数据结构化、查询性能和关系管理。

## 📋 迁移前准备

### 1. 备份现有KV数据

```bash
# 导出所有KV数据作为备份
wrangler kv:key list --binding=SUBSCRIPTION_KV > kv_backup.json
```

### 2. 确保Wrangler版本

确保您使用的是支持D1的Wrangler版本：

```bash
wrangler --version
# 需要 >= 3.0.0
```

## 🗄️ 创建D1数据库

### 1. 创建数据库

```bash
# 创建D1数据库
wrangler d1 create cf-whitelist-gateway-db
```

执行后会显示类似输出：
```
✅ Successfully created DB 'cf-whitelist-gateway-db' in region APAC
Created your database using D1's new storage backend. The new storage backend is not yet recommended for production workloads, but backs up your data via point-in-time restore.

[[d1_databases]]
binding = "DB"
database_name = "cf-whitelist-gateway-db"
database_id = "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
```

### 2. 更新wrangler.toml

将上一步获得的database_id复制到`wrangler.toml`文件中：

```toml
# D1 database for storing subscription URLs and IP whitelist
[[d1_databases]]
binding = "DB"
database_name = "cf-whitelist-gateway-db"
database_id = "你的数据库ID"
```

### 3. 初始化数据库表结构

```bash
# 执行数据库初始化脚本
wrangler d1 execute cf-whitelist-gateway-db --file=./schema.sql
```

## 🔄 数据迁移

### 1. 部署迁移脚本

首先部署包含迁移功能的Worker：

```bash
# 部署到预览环境进行测试
wrangler deploy --env preview

# 或直接部署到生产环境
wrangler deploy
```

### 2. 执行数据迁移

```bash
# 使用curl执行迁移（替换为您的Worker URL）
curl -X POST https://your-worker.your-subdomain.workers.dev/migrate
```

或者在浏览器中访问：
```
https://your-worker.your-subdomain.workers.dev/migrate
```

### 3. 验证迁移结果

迁移完成后，您会看到类似的响应：

```json
{
  "success": true,
  "message": "数据迁移完成",
  "statistics": {
    "kv_keys_found": 45,
    "proxies_found": 15,
    "whitelists_found": 12,
    "proxies_migrated": 15,
    "ips_migrated": 38,
    "final_proxy_count": 15,
    "final_ip_count": 38
  }
}
```

## 🧪 测试迁移结果

### 1. 测试现有代理

使用现有的网关ID和密码测试管理功能：

```bash
# 测试获取代理信息
curl -X POST https://your-worker.your-subdomain.workers.dev/ \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "action=manage_proxy&proxy_id=YOUR_PROXY_ID&proxy_password=YOUR_PASSWORD&sub_action=get_info"
```

### 2. 测试白名单功能

```bash
# 测试添加IP到白名单
curl -X GET "https://your-worker.your-subdomain.workers.dev/api/whitelist/YOUR_PROXY_ID?action=add"
```

### 3. 测试代理访问

```bash
# 测试代理访问
curl "https://your-worker.your-subdomain.workers.dev/proxy/YOUR_PROXY_ID"
```

## 📊 数据库结构

迁移后的D1数据库包含以下表：

### proxies表
- `id` (TEXT PRIMARY KEY): 网关ID
- `subscription_url` (TEXT UNIQUE): 订阅地址
- `password` (TEXT): 管理密码
- `created_at` (DATETIME): 创建时间
- `updated_at` (DATETIME): 更新时间

### whitelist_ips表
- `id` (INTEGER PRIMARY KEY): 自增ID
- `proxy_id` (TEXT): 网关ID（外键）
- `ip_address` (TEXT): IP地址
- `created_at` (DATETIME): 创建时间

## 🔧 性能优化

D1数据库相比KV存储的优势：

1. **结构化查询**: 支持复杂的SQL查询
2. **关系管理**: 外键约束确保数据一致性
3. **索引优化**: 自动创建的索引提高查询性能
4. **事务支持**: 确保数据操作的原子性

## 🚨 故障排除

### 常见问题

1. **数据库ID错误**
   ```
   Error: D1_ERROR: Database not found
   ```
   检查`wrangler.toml`中的`database_id`是否正确。

2. **表不存在错误**
   ```
   Error: no such table: proxies
   ```
   确保已执行`schema.sql`初始化数据库表。

3. **迁移失败**
   ```
   Error: Migration failed
   ```
   检查KV数据格式，确保有足够的权限访问KV和D1。

### 回滚方案

如果迁移出现问题，可以：

1. 保留KV配置，临时回滚到KV版本
2. 修复D1问题后重新迁移
3. 使用备份的KV数据恢复

## ✅ 迁移完成后

### 1. 清理KV数据（可选）

确认D1迁移成功后，可以清理KV数据：

```bash
# 谨慎操作：删除所有KV数据
wrangler kv:key list --binding=SUBSCRIPTION_KV | jq -r '.[] | .name' | xargs -I {} wrangler kv:key delete --binding=SUBSCRIPTION_KV "{}"
```

### 2. 更新wrangler.toml

移除KV配置（如果不再需要）：

```toml
# 可以注释或删除KV配置
# [[kv_namespaces]]
# binding = "SUBSCRIPTION_KV"
# id = "79c7b1de05d3414baae7443bb7b4e968"
```

### 3. 监控和维护

- 定期检查D1数据库使用情况
- 监控查询性能
- 设置适当的备份策略

## 📈 后续优化建议

1. **添加数据库索引**: 根据查询模式优化索引
2. **实现数据清理**: 定期清理过期的代理和IP
3. **添加统计功能**: 利用SQL查询实现使用统计
4. **批量操作**: 利用事务实现批量IP管理

---

**注意**: 迁移过程中建议在测试环境先验证，确保生产环境的稳定性。
