<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>状态检查UI测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            margin: 0;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
            text-align: center;
        }

        .header {
            margin-bottom: 30px;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.2em;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .test-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            font-size: 1em;
            font-family: 'Courier New', monospace;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin: 5px;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #1e7e34;
            transform: translateY(-2px);
        }

        .example-links {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .example-links h3 {
            color: #1565c0;
            margin-bottom: 15px;
        }

        .example-links a {
            display: block;
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 6px;
            text-decoration: none;
            color: #1565c0;
            border: 1px solid #e3f2fd;
            transition: all 0.3s ease;
        }

        .example-links a:hover {
            background: #f3e5f5;
            transform: translateY(-1px);
        }

        .note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            color: #856404;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }

            .header h1 {
                font-size: 1.8em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 状态检查UI测试</h1>
            <p>测试新的白名单状态检查界面</p>
        </div>

        <div class="test-section">
            <h2>📋 自定义测试</h2>
            <div class="form-group">
                <label for="proxyId">网关ID：</label>
                <input type="text" id="proxyId" placeholder="输入网关ID，例如：4yqj96pexcyb4oh8bo0psg">
            </div>
            <button class="btn btn-primary" onclick="openStatusPage()">
                🔍 查看状态页面
            </button>
        </div>

        <div class="example-links">
            <h3>📝 示例链接</h3>
            <p>以下是一些示例状态检查页面链接：</p>
            
            <a href="/status/4yqj96pexcyb4oh8bo0psg" target="_blank">
                📊 示例代理状态检查 (4yqj96pexcyb4oh8bo0psg)
            </a>
            
            <a href="/status/nonexistent" target="_blank">
                ❌ 不存在的代理测试 (nonexistent)
            </a>
            
            <a href="/status/" target="_blank">
                ⚠️ 无效网关ID测试 (空ID)
            </a>
        </div>

        <div class="note">
            <strong>💡 说明：</strong>
            <ul style="text-align: left; margin-top: 10px;">
                <li>新的状态检查页面提供了美观的UI界面</li>
                <li>显示当前IP的白名单状态</li>
                <li>列出所有白名单IP地址</li>
                <li>提供快速添加IP和刷新功能</li>
                <li>支持响应式设计，适配移动设备</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🛠️ 相关工具</h2>
            <a href="/admin" class="btn btn-primary">
                🏠 管理页面
            </a>
            <a href="/whitelist-helper" class="btn btn-success">
                🛠️ 白名单工具
            </a>
        </div>
    </div>

    <script>
        function openStatusPage() {
            const proxyId = document.getElementById('proxyId').value.trim();
            
            if (!proxyId) {
                alert('请输入网关ID');
                return;
            }
            
            const url = `/status/${proxyId}`;
            window.open(url, '_blank');
        }

        // 回车键快捷操作
        document.getElementById('proxyId').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                openStatusPage();
            }
        });
    </script>
</body>
</html>
