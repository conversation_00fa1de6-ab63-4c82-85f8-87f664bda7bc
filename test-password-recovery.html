<!DOCTYPE html>
<html>
<head>
    <title>密码找回功能测试</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
        }

        .content {
            padding: 40px;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
            font-size: 0.95em;
        }

        .form-group input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1em;
            transition: all 0.3s ease;
            background: #fafbfc;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4facfe;
            background: white;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .form-group small {
            display: block;
            margin-top: 5px;
            color: #666;
            font-size: 0.85em;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .result {
            margin-top: 25px;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .result.success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border-left-color: #28a745;
            color: #155724;
        }

        .result.error {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border-left-color: #dc3545;
            color: #721c24;
        }

        .result h3 {
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .result p {
            margin-bottom: 10px;
            line-height: 1.6;
        }

        .result a {
            color: inherit;
            text-decoration: none;
            font-weight: 600;
            border-bottom: 1px dashed currentColor;
        }

        .result a:hover {
            border-bottom-style: solid;
        }

        .info-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 1px solid #2196f3;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
        }

        .info-box strong {
            color: #1976d2;
        }

        .link-style {
            color: #007cba;
            text-decoration: none;
            font-size: 14px;
            cursor: pointer;
        }

        .link-style:hover {
            text-decoration: underline;
        }

        .section {
            margin-bottom: 30px;
        }

        .section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5em;
        }

        .demo-data {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 密码找回功能测试</h1>
            <p>测试通过订阅地址找回代理密码的功能</p>
        </div>

        <div class="content">
            <!-- 功能说明 -->
            <div class="card section">
                <h2>📋 功能说明</h2>
                <p>这个页面用于测试新增的密码找回功能。用户可以通过输入原始订阅地址来找回对应的代理ID和管理密码。</p>
                
                <div class="info-box">
                    <strong>🔧 实现原理：</strong><br>
                    • 系统通过 <code>url_to_proxy:订阅地址</code> 键查找对应的代理ID<br>
                    • 然后通过 <code>pwd:代理ID</code> 键获取管理密码<br>
                    • 返回完整的代理信息供用户使用
                </div>
            </div>

            <!-- 测试数据 -->
            <div class="card section">
                <h2>🧪 测试数据</h2>
                <p>为了测试功能，您需要先创建一个代理。以下是一些示例数据：</p>
                
                <div class="demo-data">
                    示例订阅地址：https://example.com/subscribe/test123<br>
                    （请使用您实际创建代理时使用的订阅地址）
                </div>
            </div>

            <!-- 密码找回表单 -->
            <div class="card section">
                <h2>🔍 密码找回测试</h2>
                
                <form id="passwordRecoverForm">
                    <div class="form-group">
                        <label>📡 原始订阅地址</label>
                        <input type="text" id="recoverSubscriptionUrl" placeholder="请输入完整的原始订阅地址" required>
                        <small>输入创建代理时使用的原始订阅地址</small>
                    </div>
                    <button type="submit" class="btn">🔍 查找密码</button>
                </form>
                
                <div id="recoverResult"></div>
            </div>

            <!-- 使用说明 -->
            <div class="card section">
                <h2>📖 使用说明</h2>
                <ol style="line-height: 1.8; padding-left: 20px;">
                    <li>在上方输入框中输入您之前创建代理时使用的完整订阅地址</li>
                    <li>点击"查找密码"按钮</li>
                    <li>如果订阅地址存在，系统将显示对应的代理ID和管理密码</li>
                    <li>您可以使用找回的信息登录管理界面</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        // 复制到剪贴板功能
        function copyToClipboard(text, buttonElement) {
            navigator.clipboard.writeText(text).then(function() {
                const originalText = buttonElement.textContent;
                buttonElement.textContent = '✅ 已复制';
                buttonElement.style.background = '#28a745';
                setTimeout(() => {
                    buttonElement.textContent = originalText;
                    buttonElement.style.background = '';
                }, 2000);
            }, function(err) {
                console.error('无法复制文本: ', err);
                alert('复制失败，请手动复制');
            });
        }

        // 密码找回表单处理
        document.getElementById('passwordRecoverForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const submitBtn = e.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = '处理中...';
            submitBtn.disabled = true;

            const formData = new FormData();
            formData.append('action', 'recover_password');
            formData.append('subscription_url', document.getElementById('recoverSubscriptionUrl').value);

            try {
                const response = await fetch('/', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    document.getElementById('recoverResult').innerHTML = `
                        <div class="result success">
                            <h3>🎉 密码找回成功！</h3>
                            <p><strong>🆔 代理ID：</strong> <code style="background: #f8f9fa; padding: 2px 6px; border-radius: 4px; font-family: monospace;">${result.proxy_id}</code>
                               <button onclick="copyToClipboard('${result.proxy_id}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;">复制</button></p>
                            <p><strong>🔐 管理密码：</strong> <code style="background: #fff3cd; padding: 2px 6px; border-radius: 4px; font-family: monospace; color: #856404;">${result.proxy_password}</code>
                               <button onclick="copyToClipboard('${result.proxy_password}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;">复制</button></p>
                            <p><strong>📡 订阅地址：</strong> <a href="${result.subscription_url}" target="_blank">${result.subscription_url}</a></p>
                            <p><strong>🔗 代理地址：</strong> <a href="${result.proxy_url}" target="_blank">${result.proxy_url}</a>
                               <button onclick="copyToClipboard('${result.proxy_url}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;">复制</button></p>
                            <p><strong>✨ 白名单地址：</strong> <a href="${result.whitelist_url}" target="_blank">${result.whitelist_url}</a>
                               <button onclick="copyToClipboard('${result.whitelist_url}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;">复制</button></p>
                            <p><strong>🎨 状态检查页面：</strong> <a href="${result.status_url}" target="_blank">${result.status_url}</a>
                               <button onclick="copyToClipboard('${result.status_url}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;">复制</button></p>
                            <div class="info-box">
                                <strong>💡 提示：</strong><br>
                                • 现在您可以使用找回的代理ID和密码登录管理界面<br>
                                • 请妥善保存这些信息，避免再次丢失<br>
                                • 您可以访问主页进行登录管理
                            </div>
                        </div>
                    `;
                    // 清空输入框
                    document.getElementById('recoverSubscriptionUrl').value = '';
                } else {
                    document.getElementById('recoverResult').innerHTML = `
                        <div class="result error">
                            <h3>❌ 密码找回失败</h3>
                            <p>错误详情：${result.error || '未知错误'}</p>
                            <p>请检查订阅地址是否正确，或确认该订阅地址是否已创建过代理。</p>
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('recoverResult').innerHTML = `
                    <div class="result error">
                        <h3>❌ 网络错误</h3>
                        <p>错误详情：${error.message}</p>
                        <p>请确保服务正在运行，或检查网络连接。</p>
                    </div>
                `;
            } finally {
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
        });
    </script>
</body>
</html>
