# D1数据库部署验证清单

## 📋 部署前检查

### 环境准备
- [ ] 已安装Node.js (>= 16.0.0)
- [ ] 已安装Wrangler CLI (>= 3.0.0)
- [ ] 已登录Cloudflare账户 (`wrangler whoami`)
- [ ] 已克隆项目代码到本地

### 依赖安装
- [ ] 执行 `npm install` 安装项目依赖
- [ ] 确认package.json中的脚本可用

## 🗄️ D1数据库设置

### 自动设置（推荐）
- [ ] 执行 `chmod +x setup-d1.sh` 给脚本执行权限
- [ ] 运行 `./setup-d1.sh` 自动设置D1数据库
- [ ] 确认脚本执行成功，获得数据库ID

### 手动设置（备选）
- [ ] 执行 `wrangler d1 create cf-whitelist-gateway-db`
- [ ] 复制返回的database_id到wrangler.toml
- [ ] **重要：初始化数据库表结构**
  ```bash
  # 本地环境
  wrangler d1 execute cf-whitelist-gateway-db --local --file=./schema.sql
  # 生产环境
  wrangler d1 execute cf-whitelist-gateway-db --remote --file=./schema.sql
  ```
- [ ] 验证表结构创建成功
  ```bash
  wrangler d1 execute cf-whitelist-gateway-db --local --command="SELECT name FROM sqlite_master WHERE type='table';"
  ```

### 配置验证
- [ ] 检查 `wrangler.toml` 中的D1配置：
  ```toml
  [[d1_databases]]
  binding = "DB"
  database_name = "cf-whitelist-gateway-db"
  database_id = "你的数据库ID"
  ```
- [ ] 确认database_id不为空

## 🚀 部署验证

### 本地测试
- [ ] 执行 `wrangler dev` 启动本地开发服务器
- [ ] 访问 `http://localhost:8787/` 确认首页正常
- [ ] 测试创建新代理功能
- [ ] 测试管理现有代理功能

### 生产部署
- [ ] 执行 `wrangler deploy` 部署到生产环境
- [ ] 记录部署后的Worker URL
- [ ] 确认部署成功无错误

### 功能测试
- [ ] 访问生产环境首页
- [ ] 测试创建新代理：
  - [ ] 输入测试订阅地址
  - [ ] 确认返回代理ID和密码
  - [ ] 记录生成的信息用于后续测试
- [ ] 测试白名单功能：
  - [ ] 访问白名单添加URL
  - [ ] 确认IP成功添加
  - [ ] 访问状态检查页面验证
- [ ] 测试代理访问：
  - [ ] 使用代理URL访问
  - [ ] 确认白名单IP可以访问
  - [ ] 确认非白名单IP被拒绝

## 🔄 数据迁移（如适用）

### 迁移前准备
- [ ] 备份现有KV数据：`wrangler kv:key list --binding=SUBSCRIPTION_KV > kv_backup.json`
- [ ] 确认KV数据完整性
- [ ] 记录现有代理数量

### 执行迁移
- [ ] 访问 `https://your-worker.workers.dev/migrate` 执行迁移
- [ ] 或使用curl：`curl -X POST https://your-worker.workers.dev/migrate`
- [ ] 确认迁移成功响应
- [ ] 记录迁移统计信息

### 迁移验证
- [ ] 验证代理数量一致
- [ ] 验证白名单IP数量一致
- [ ] 测试现有代理功能
- [ ] 测试密码找回功能

## 🧪 高级测试

### D1数据库测试
- [ ] 访问 `https://your-worker.workers.dev/test-d1` 运行数据库测试
- [ ] 确认所有测试通过
- [ ] 检查测试报告中的详细信息

### 性能测试
- [ ] 测试大量IP的白名单操作
- [ ] 测试并发代理创建
- [ ] 监控响应时间

### 错误处理测试
- [ ] 测试无效代理ID访问
- [ ] 测试错误密码登录
- [ ] 测试重复订阅地址创建
- [ ] 测试IP限制功能（如已配置）

## 📊 监控设置

### Cloudflare Dashboard
- [ ] 检查Workers使用情况
- [ ] 检查D1数据库使用情况
- [ ] 设置使用量警报（可选）

### 日志监控
- [ ] 检查Worker日志是否正常
- [ ] 确认无错误日志
- [ ] 设置日志监控（可选）

## 🔧 故障排除

### 常见错误及解决方案

#### 1. 数据库表不存在错误
**错误信息：**
```
D1_ERROR: no such table: proxies: SQLITE_ERROR
```
**解决方案：**
- [ ] 确认 `schema.sql` 文件存在
- [ ] 运行数据库初始化命令：
  ```bash
  wrangler d1 execute cf-whitelist-gateway-db --local --file=./schema.sql
  wrangler d1 execute cf-whitelist-gateway-db --remote --file=./schema.sql
  ```
- [ ] 验证表已创建：
  ```bash
  wrangler d1 execute cf-whitelist-gateway-db --local --command="SELECT name FROM sqlite_master WHERE type='table';"
  ```

#### 2. 前端网络错误
**错误信息：**
```
Unexpected token '<'
```
**原因：** 通常是数据库表未初始化，服务器返回HTML错误页面而非JSON
**解决方案：**
- [ ] 按照上述步骤初始化数据库表
- [ ] 重新测试创建代理功能

#### 3. 其他常见问题
- [ ] 如果数据库连接失败，检查database_id配置
- [ ] 如果迁移失败，检查KV权限和数据格式
- [ ] 如果功能异常，检查Worker日志

### 回滚准备
- [ ] 保留KV配置作为备份
- [ ] 准备回滚到KV版本的代码
- [ ] 确认数据备份可用

## ✅ 部署完成确认

### 最终检查
- [ ] 所有核心功能正常工作
- [ ] 数据迁移完成（如适用）
- [ ] 性能表现良好
- [ ] 错误处理正确
- [ ] 监控设置完成

### 文档更新
- [ ] 更新内部文档中的URL
- [ ] 通知用户新的功能特性
- [ ] 更新API文档（如有变化）

### 清理工作
- [ ] 删除测试数据
- [ ] 清理临时文件
- [ ] 移除不需要的KV数据（谨慎操作）

## 📝 部署记录

### 部署信息
- 部署日期：__________
- 部署人员：__________
- Worker URL：__________
- 数据库ID：__________

### 迁移统计（如适用）
- 迁移的代理数量：__________
- 迁移的IP数量：__________
- 迁移耗时：__________

### 问题记录
- 遇到的问题：__________
- 解决方案：__________
- 注意事项：__________

---

**注意**：请按顺序完成所有检查项，确保部署的稳定性和可靠性。如遇问题，请参考相关文档或联系技术支持。
