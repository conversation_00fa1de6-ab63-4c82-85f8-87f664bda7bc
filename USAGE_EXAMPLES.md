# 使用示例

## 🚀 快速开始

### 场景1：创建第一个代理

1. **访问服务首页**
   ```
   https://your-worker.your-subdomain.workers.dev/
   ```

2. **创建新代理**
   - 选择 "🆕 创建新代理" 模式
   - 输入订阅地址：`https://example.com/subscribe/abc123`
   - 点击 "创建代理"

3. **保存重要信息**
   ```
   代理ID: xyz789abc
   管理密码: Kj8mN2pQ9rS5
   代理地址: https://your-worker.your-subdomain.workers.dev/proxy/xyz789abc
   白名单地址: https://your-worker.your-subdomain.workers.dev/api/whitelist/xyz789abc?action=add
   状态检查页面: https://your-worker.your-subdomain.workers.dev/status/xyz789abc
   ```

### 场景2：管理现有代理

1. **登录管理界面**
   - 选择 "🛠️ 管理现有代理" 模式
   - 输入代理ID：`xyz789abc`
   - 输入管理密码：`Kj8mN2pQ9rS5`
   - 点击 "登录管理"

2. **查看代理信息**
   - 自动显示代理详细信息
   - 查看当前白名单IP列表
   - 复制各种链接

3. **添加IP到白名单**
   - 切换到 "➕ 添加IP" 标签
   - 输入IP地址：`*************`
   - 点击 "添加IP到白名单"

## 📋 常用操作

### 用户自助添加IP

**方法1：直接访问链接**
```
https://your-worker.your-subdomain.workers.dev/api/whitelist/xyz789abc?action=add
```
用户在浏览器中访问此链接，系统自动添加其IP到白名单。

**方法2：使用白名单助手**
```
https://your-worker.your-subdomain.workers.dev/whitelist-helper
```
输入代理ID，点击"将我的IP添加到白名单"。

**方法3：查看状态页面**
```
https://your-worker.your-subdomain.workers.dev/status/xyz789abc
```
如果IP不在白名单中，页面会显示"添加我的IP"按钮。

### 检查白名单状态

**UI界面（推荐）**
```
https://your-worker.your-subdomain.workers.dev/status/xyz789abc
```
显示美观的状态界面，包含：
- 当前IP的白名单状态
- 所有白名单IP列表
- 快速操作按钮

**JSON接口**
```bash
curl https://your-worker.your-subdomain.workers.dev/api/whitelist/xyz789abc
```
返回JSON格式的状态信息：
```json
{
  "whitelisted": true,
  "ip": "*************",
  "proxy_id": "xyz789abc",
  "total_ips": 3,
  "all_ips": ["*************", "********", "**********"]
}
```

## 🔧 高级用法

### 批量管理多个代理

如果您需要管理多个代理，建议：

1. **记录管理信息**
   ```
   代理1: ID=abc123, 密码=Xx1Yy2Zz3, 用途=家庭网络
   代理2: ID=def456, 密码=Aa4Bb5Cc6, 用途=办公网络
   代理3: ID=ghi789, 密码=Dd7Ee8Ff9, 用途=移动设备
   ```

2. **使用书签管理**
   - 将管理页面添加到浏览器书签
   - 为每个代理创建状态检查页面书签

### 分享给用户

**给用户的简单说明**
```
您的专属代理已创建！

📱 添加IP到白名单（必须先做）：
点击这个链接 → https://your-worker.your-subdomain.workers.dev/api/whitelist/xyz789abc?action=add

🚀 代理地址（添加IP后使用）：
https://your-worker.your-subdomain.workers.dev/proxy/xyz789abc

📊 查看状态：
https://your-worker.your-subdomain.workers.dev/status/xyz789abc

注意：每次更换网络环境时，需要重新添加新的IP到白名单。
```

## 🛠️ 开发者集成

### API调用示例

**JavaScript**
```javascript
// 检查白名单状态
async function checkWhitelist(proxyId) {
  const response = await fetch(`/api/whitelist/${proxyId}`);
  const data = await response.json();
  return data.whitelisted;
}

// 添加当前IP到白名单
async function addToWhitelist(proxyId) {
  const response = await fetch(`/api/whitelist/${proxyId}?action=add`);
  return response.ok;
}
```

**Python**
```python
import requests

def check_whitelist(base_url, proxy_id):
    response = requests.get(f"{base_url}/api/whitelist/{proxy_id}")
    return response.json()

def add_to_whitelist(base_url, proxy_id):
    response = requests.get(f"{base_url}/api/whitelist/{proxy_id}?action=add")
    return response.status_code == 200
```

### 自动化脚本

**批量添加IP**
```bash
#!/bin/bash
PROXY_ID="xyz789abc"
BASE_URL="https://your-worker.your-subdomain.workers.dev"

# 添加多个IP到白名单（需要管理密码）
IPS=("*************" "*************" "*************")

for ip in "${IPS[@]}"; do
  echo "Adding IP: $ip"
  # 注意：这需要通过管理界面或API实现
done
```

## ⚠️ 注意事项

### 安全提醒

1. **管理密码**：
   - 创建代理时生成的管理密码无法恢复
   - 请务必保存在安全的地方
   - 不要分享给不信任的人

2. **公开服务**：
   - 任何人都可以创建代理
   - 请合理使用资源
   - 不要创建过多不必要的代理

3. **IP白名单**：
   - 只有白名单中的IP才能访问代理
   - 动态IP用户需要定期更新白名单
   - 建议定期清理无用的IP

### 最佳实践

1. **命名规范**：为代理创建有意义的标识
2. **定期维护**：清理不再使用的IP
3. **备份信息**：保存重要的代理信息
4. **监控使用**：定期检查代理状态

---

**提示**：如果您是从旧版本升级，现有的代理仍然可以正常工作，但需要通过新的管理界面来管理白名单。
