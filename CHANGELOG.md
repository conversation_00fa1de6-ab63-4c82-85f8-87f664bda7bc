# 更新日志

## [3.0.0] - 2024-01-XX

### 🚀 重大变更
- **数据存储迁移**：从Cloudflare KV存储迁移到D1数据库
- **性能提升**：利用D1数据库的SQL查询能力，提高数据检索和管理效率
- **数据结构优化**：使用关系型数据库设计，支持外键约束和索引优化

### 新增
- ✨ **D1数据库支持**：完整的D1数据库集成
- 📊 **数据库表结构**：
  - `proxies`表：存储代理信息（ID、订阅地址、密码）
  - `whitelist_ips`表：存储白名单IP，支持外键关联
- 🔄 **自动化迁移**：
  - 数据迁移脚本 (`migrate-kv-to-d1.js`)
  - 自动化设置脚本 (`setup-d1.sh`)
  - 详细迁移指南 (`D1_MIGRATION_GUIDE.md`)
- 📈 **性能优化**：
  - 自动创建数据库索引
  - 支持复杂SQL查询
  - 事务支持确保数据一致性
- 🛠️ **开发工具**：
  - 数据库初始化脚本 (`schema.sql`)
  - 一键设置脚本

### 变更
- 🔧 **数据操作重构**：所有KV操作替换为D1 SQL查询
- 📝 **配置更新**：`wrangler.toml`添加D1数据库配置
- 📚 **文档更新**：README和部署指南更新为D1数据库说明
- 🎯 **API保持兼容**：所有现有API接口保持不变，确保客户端兼容性

### 技术改进
- **查询性能**：利用SQL索引提高查询速度
- **数据一致性**：外键约束确保数据关系完整性
- **扩展性**：为未来功能扩展提供更好的数据基础
- **维护性**：结构化数据便于管理和调试

### 迁移指南
- 📋 **向后兼容**：现有KV数据可通过迁移脚本无缝迁移
- 🔄 **零停机迁移**：支持在线数据迁移
- 📊 **迁移验证**：提供详细的迁移统计和验证

### 破坏性变更
- ⚠️ **存储后端变更**：从KV存储变更为D1数据库
- 🔧 **配置要求**：需要创建和配置D1数据库
- 📦 **部署依赖**：需要执行数据库初始化脚本

---

## [2.2.0] - 2024-12-19

### 🔍 密码找回功能版本

#### ✨ 新增功能

- **🔍 密码找回功能**：
  - 用户可以通过输入原始订阅地址找回网关ID和管理密码
  - 在管理页面添加"忘记密码？通过订阅地址找回"链接
  - 提供专门的密码找回表单界面
  - 支持登录表单和密码找回表单之间的切换

- **🎨 界面优化**：
  - 新增密码找回表单，包含订阅地址输入框
  - 添加"返回登录"链接，方便用户切换
  - 优化表单切换动画和用户体验
  - 完整的成功/失败状态显示

- **📋 信息展示**：
  - 找回成功后显示完整的网关信息
  - 包含网关ID、管理密码、网关地址、白名单地址等
  - 所有重要信息都提供一键复制功能
  - 详细的使用提示和操作指导

#### 🔧 技术实现

- **后端API**：
  - 新增 `recover_password` 操作处理密码找回请求
  - 利用现有的 `url_to_proxy:订阅地址` 映射查找网关ID
  - 通过 `pwd:网关ID` 获取管理密码
  - 返回完整的网关管理信息

- **前端逻辑**：
  - JavaScript处理表单切换逻辑
  - 异步提交密码找回请求
  - 动态显示查找结果
  - 完善的错误处理和用户反馈

- **数据安全**：
  - 只返回与输入订阅地址匹配的信息
  - 不会泄露其他用户的代理信息
  - 详细的错误提示，不暴露系统内部信息

#### 🧪 测试支持

- **测试页面**：创建 `test-password-recovery.html` 用于功能测试
- **功能文档**：新增 `PASSWORD_RECOVERY_FEATURE.md` 详细说明
- **使用指南**：完整的测试步骤和使用说明

#### 💡 使用场景

- 用户忘记代理密码但记得订阅地址
- 需要重新获取代理管理信息
- 快速恢复对代理的管理权限

---

## [2.1.0] - 2024-12-19

### 🚀 功能增强版本

#### ✨ 新增功能

- **🗑️ IP删除功能**：
  - 管理界面白名单IP列表添加删除按钮
  - 支持一键删除不需要的IP地址
  - 删除前确认对话框，防止误操作
  - 删除后自动刷新列表显示

- **🌍 域名限制配置**：
  - 新增 `ALLOWED_DOMAINS` 环境变量
  - 支持限制允许代理的订阅地址域名
  - 多域名支持，用逗号分隔
  - 提高服务安全性，防止滥用

- **📈 IP数量限制**：
  - 新增 `MAX_WHITELIST_IPS` 环境变量
  - 可配置每个代理的白名单IP上限
  - 防止单个代理占用过多资源
  - 在添加IP时实时检查限制

- **🔍 多种查询方式**：
  - 管理界面支持使用网关ID查询
  - 新增支持使用完整订阅地址查询
  - 自动识别输入类型并处理
  - 提高用户使用便利性

- **🚫 防重复机制**：
  - 检测重复的订阅地址创建
  - 避免相同订阅地址的KV存储冗余
  - 提示现有网关ID，引导用户管理
  - 优化存储空间使用

#### 🔄 改进优化

- **📝 界面优化**：
  - 更新管理界面输入框标签
  - 明确支持网关ID或订阅地址输入
  - 优化白名单IP列表布局
  - 添加删除按钮和更好的视觉效果

- **🔒 安全增强**：
  - 多环节验证IP数量限制
  - 域名白名单验证
  - 更详细的错误信息提示
  - 防止恶意使用和资源滥用

- **🛠️ 技术改进**：
  - 添加 `url_to_proxy:订阅地址` 映射
  - 优化KV存储结构
  - 改进API接口设计
  - 增强错误处理机制

#### 📚 文档更新

- **README.md**：
  - 添加新功能使用说明
  - 更新环境变量配置指南
  - 完善安全说明和注意事项
  - 添加防重复机制说明

- **测试文件**：
  - 新增 `test-new-features.html` 测试页面
  - 提供完整的功能测试指南
  - 包含所有新功能的测试步骤

#### ⚙️ 配置变更

- **环境变量**：
  ```toml
  [vars]
  ALLOWED_DOMAINS = ""      # 新增：域名限制
  MAX_WHITELIST_IPS = ""    # 新增：IP数量限制
  ```

#### 🔧 API更新

- **新增删除IP接口**：
  - `POST /` with `sub_action=remove_ip`
  - 支持通过管理密码删除指定IP
  - 返回删除结果和更新后的统计信息

---

## [2.0.0] - 2024-12-19

### 🚀 重大更新：公开服务模式

#### ✨ 新增功能

- **🆓 公开服务**：移除管理员密码限制，任何人都可以创建代理
- **🔐 独立管理**：每个代理生成独立的管理密码
- **🎛️ 双模式界面**：
  - 创建新网关模式
  - 管理现有网关模式（需要网关ID + 密码）
- **📊 完整管理界面**：
  - 查看网关详细信息
  - 显示白名单IP列表
  - 添加IP到白名单
  - 标签页式管理界面
- **🎨 美观UI设计**：
  - 模式切换按钮
  - 标签页导航
  - 响应式布局
  - 现代化视觉效果

#### 🔄 重大变更

- **路由更新**：
  - `/admin` 功能迁移到 `/`（首页）
  - 保持 `/admin` 路由兼容性（重定向到首页）
- **数据结构**：
  - 新增 `pwd:网关ID` 存储管理密码
  - 保持现有 `sub:网关ID` 和 `whitelist:网关ID` 结构
- **API响应**：
  - 创建网关时返回管理密码
  - 管理接口需要密码验证

#### 🛠️ 技术改进

- **JavaScript重构**：
  - 模式切换功能
  - 标签页管理
  - 表单处理优化
- **CSS增强**：
  - 新增模式切换样式
  - 标签页样式
  - 改进的响应式设计
- **错误处理**：
  - 更友好的错误提示
  - 详细的状态反馈

#### 📝 文档更新

- **README.md**：更新为公开服务说明
- **部署指南**：新增 DEPLOYMENT.md
- **配置文件**：移除 ADMIN_PASSWORD 环境变量

#### ⚠️ 破坏性变更

- **环境变量**：不再需要 `ADMIN_PASSWORD`
- **管理方式**：从集中管理改为分散管理
- **访问控制**：从管理员控制改为用户自主控制

#### 🔒 安全考虑

- 每个代理的管理密码独立生成
- 管理密码无法恢复，需要用户妥善保存
- 白名单仍受密码保护，确保安全性

---

## [1.0.0] - 2024-12-18

### 🎉 初始版本

#### ✨ 核心功能

- **🔐 管理员模式**：需要管理员密码访问
- **🌐 订阅网关**：将原始订阅地址转换为网关地址
- **🔒 IP白名单**：只有白名单中的IP才能访问网关
- **🎨 状态检查UI**：美观的白名单状态查看界面
- **📱 响应式设计**：支持移动设备访问

#### 🛠️ 技术特性

- 基于 Cloudflare Workers
- 使用 Cloudflare KV 存储
- 现代化 Web 界面
- RESTful API 设计

#### 📊 功能模块

- **管理界面**：`/admin`
- **API接口**：`/api/whitelist/{proxy_id}`
- **网关服务**：`/proxy/{proxy_id}`
- **状态检查**：`/status/{proxy_id}`
- **白名单助手**：`/whitelist-helper`

---

## 版本说明

### 版本号规则

采用语义化版本控制（Semantic Versioning）：
- **主版本号**：不兼容的API修改
- **次版本号**：向下兼容的功能性新增
- **修订号**：向下兼容的问题修正

### 升级指南

#### 从 1.x 升级到 2.0

1. **备份数据**：
   ```bash
   wrangler kv:key list --binding=SUBSCRIPTION_KV > backup.json
   ```

2. **更新配置**：
   - 移除 `wrangler.toml` 中的 `ADMIN_PASSWORD`
   - 部署新版本代码

3. **数据兼容性**：
   - 现有代理继续工作
   - 现有白名单保持有效
   - 需要为现有代理生成管理密码（如需管理）

4. **用户通知**：
   - 通知用户新的管理方式
   - 提供新的管理界面链接
   - 说明管理密码的重要性

### 未来计划

- **2.1.0**：批量管理功能
- **2.2.0**：使用统计和分析
- **2.3.0**：API密钥管理
- **3.0.0**：多租户支持

---

**注意**：升级前请务必备份数据，并在测试环境中验证新功能。
