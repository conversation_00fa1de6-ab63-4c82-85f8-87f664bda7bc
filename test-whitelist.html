<!DOCTYPE html>
<html>
<head>
    <title>白名单测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 600px; margin: 0 auto; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; cursor: pointer; margin: 5px; }
        .result { background: #f0f0f0; padding: 15px; margin: 20px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>代理ID白名单测试：4yqj96pexcyb4oh8bo0psg</h1>

        <h2>🎉 新的简便方法 - 只需点击！</h2>
        <p><strong>添加IP地址：</strong> <a href="https://cf-subscription-proxy.huang9832.workers.dev/api/whitelist/4yqj96pexcyb4oh8bo0psg?action=add" target="_blank">点击这里添加您的IP</a></p>

        <h2>API测试</h2>
        <button onclick="addIP()">将我的IP添加到白名单 (GET请求)</button>
        <button onclick="checkStatus()">检查白名单状态 (GET请求)</button>

        <div id="result"></div>
    </div>

    <script>
        const proxyId = '4yqj96pexcyb4oh8bo0psg';
        const baseUrl = 'https://cf-subscription-proxy.huang9832.workers.dev'; // 替换为你的实际URL

        async function addIP() {
            try {
                const response = await fetch(`${baseUrl}/api/whitelist/${proxyId}?action=add`, {
                    method: 'GET'
                });

                // 检查响应是HTML（成功页面）还是JSON
                const contentType = response.headers.get('content-type');

                if (contentType && contentType.includes('text/html')) {
                    document.getElementById('result').innerHTML = `
                        <div class="result">
                            <h3>添加IP结果 (GET请求)</h3>
                            <p>✅ 成功！您的IP已添加到白名单。</p>
                            <p>服务器返回了HTML成功页面。</p>
                        </div>
                    `;
                } else {
                    const result = await response.json();
                    document.getElementById('result').innerHTML = `
                        <div class="result">
                            <h3>添加IP结果 (GET请求)</h3>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('result').innerHTML = `
                    <div class="result">
                        <h3>错误</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        async function checkStatus() {
            try {
                const response = await fetch(`${baseUrl}/api/whitelist/${proxyId}`, {
                    method: 'GET'
                });

                const result = await response.json();

                document.getElementById('result').innerHTML = `
                    <div class="result">
                        <h3>检查状态结果 (GET请求)</h3>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                document.getElementById('result').innerHTML = `
                    <div class="result">
                        <h3>错误</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
