#!/bin/bash

# Cloudflare Workers D1数据库设置脚本
# 用于自动化设置D1数据库和迁移数据

set -e

echo "🚀 Cloudflare Workers D1数据库设置脚本"
echo "========================================"

# 检查wrangler是否安装
if ! command -v wrangler &> /dev/null; then
    echo "❌ 错误: 未找到wrangler命令"
    echo "请先安装wrangler: npm install -g wrangler"
    exit 1
fi

# 检查wrangler版本
WRANGLER_VERSION=$(wrangler --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
echo "📋 检测到wrangler版本: $WRANGLER_VERSION"

# 检查是否已登录
if ! wrangler whoami &> /dev/null; then
    echo "❌ 错误: 未登录Cloudflare账户"
    echo "请先登录: wrangler login"
    exit 1
fi

echo "✅ wrangler已安装并已登录"

# 数据库名称
DB_NAME="cf-whitelist-gateway-db"

echo ""
echo "📊 创建D1数据库..."

# 创建D1数据库
DB_OUTPUT=$(wrangler d1 create $DB_NAME 2>&1)

if echo "$DB_OUTPUT" | grep -q "already exists"; then
    echo "⚠️  数据库已存在，跳过创建步骤"
    # 获取现有数据库ID
    DB_ID=$(wrangler d1 list | grep $DB_NAME | awk '{print $1}')
else
    echo "✅ 数据库创建成功"
    # 从输出中提取数据库ID
    DB_ID=$(echo "$DB_OUTPUT" | grep -oE '[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}')
fi

if [ -z "$DB_ID" ]; then
    echo "❌ 错误: 无法获取数据库ID"
    exit 1
fi

echo "📋 数据库ID: $DB_ID"

echo ""
echo "📝 更新wrangler.toml配置..."

# 备份原始配置文件
cp wrangler.toml wrangler.toml.backup

# 检查是否已存在D1配置
if grep -q "database_id.*=" wrangler.toml; then
    # 更新现有的database_id
    sed -i.bak "s/database_id = \".*\"/database_id = \"$DB_ID\"/" wrangler.toml
    echo "✅ 已更新现有的database_id"
else
    # 检查是否有空的database_id行
    if grep -q 'database_id = ""' wrangler.toml; then
        sed -i.bak "s/database_id = \"\"/database_id = \"$DB_ID\"/" wrangler.toml
        echo "✅ 已填充database_id"
    else
        echo "⚠️  请手动更新wrangler.toml中的database_id为: $DB_ID"
    fi
fi

echo ""
echo "🗄️ 初始化数据库表结构..."

# 检查schema.sql是否存在
if [ ! -f "schema.sql" ]; then
    echo "❌ 错误: 未找到schema.sql文件"
    echo "请确保schema.sql文件存在于当前目录"
    exit 1
fi

# 执行数据库初始化
if wrangler d1 execute $DB_NAME --file=./schema.sql; then
    echo "✅ 数据库表结构初始化成功"
else
    echo "❌ 错误: 数据库表结构初始化失败"
    exit 1
fi

echo ""
echo "🔄 检查是否需要数据迁移..."

# 检查是否有KV数据需要迁移
KV_KEYS=$(wrangler kv:key list --binding=SUBSCRIPTION_KV 2>/dev/null | jq length 2>/dev/null || echo "0")

if [ "$KV_KEYS" -gt 0 ]; then
    echo "📦 检测到 $KV_KEYS 个KV键，建议进行数据迁移"
    echo ""
    echo "数据迁移步骤："
    echo "1. 部署Worker: wrangler deploy"
    echo "2. 执行迁移: curl -X POST https://your-worker.your-subdomain.workers.dev/migrate"
    echo ""
    read -p "是否现在部署Worker? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🚀 部署Worker..."
        if wrangler deploy; then
            echo "✅ Worker部署成功"
            echo ""
            echo "请访问以下URL执行数据迁移:"
            WORKER_URL=$(wrangler whoami | grep "Account ID" | awk '{print $3}')
            echo "https://cf-whitelist-gateway.your-subdomain.workers.dev/migrate"
            echo ""
            echo "或使用curl命令:"
            echo "curl -X POST https://cf-whitelist-gateway.your-subdomain.workers.dev/migrate"
        else
            echo "❌ Worker部署失败"
        fi
    fi
else
    echo "✅ 未检测到KV数据，无需迁移"
fi

echo ""
echo "🎉 D1数据库设置完成!"
echo "========================================"
echo "数据库名称: $DB_NAME"
echo "数据库ID: $DB_ID"
echo ""
echo "下一步操作:"
echo "1. 如有KV数据，请执行数据迁移"
echo "2. 测试应用功能"
echo "3. 监控数据库使用情况"
echo ""
echo "配置文件备份: wrangler.toml.backup"
echo "如有问题，请参考 D1_MIGRATION_GUIDE.md"
